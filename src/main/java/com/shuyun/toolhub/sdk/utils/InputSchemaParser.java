package com.shuyun.toolhub.sdk.utils;

import cn.hutool.json.JSONObject;
import com.shuyun.toolhub.sdk.annotations.KylinToolParam;
import com.shuyun.toolhub.sdk.enums.HttpMethod;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 输入参数解析器
 *
 * <p>
 * Create on: 2025-05-28 / 星期三 / 下午 17:10
 * <AUTHOR>
 * @since v1.0.0
 */
public class InputSchemaParser {

    public static String parse(HttpMethod httpMethod, Method method) {
        return httpMethod == HttpMethod.GET ? parseGet(method) : parseBody(method);
    }

    /**
     * 只根据第一个参数，生成 JsonSchema
     */
    private static String parseBody(Method method) {
        Class<?>[] parameterTypes = method.getParameterTypes();
        if (parameterTypes.length == 0) {
            return "{}"; // 如果没有参数，返回空的 JSON 对象
        }

        Class<?> firstParamType = parameterTypes[0];
        String x = JsonSchemaUtils.generateSchema(firstParamType);
        collectToolParamAnnotation(firstParamType);

        //x = processToolParamAnnotation(x);
        return x;
    }

    /**
     * 返回 JsonPath + KylinToolParam 注解的 Map
     */
    private static Map<String, KylinToolParam> collectToolParamAnnotation(Class<?> firstParamType) {
        Map<String, KylinToolParam> annotationMap = new HashMap<>();
        collectFieldAnnotations(firstParamType, "", annotationMap);
        return annotationMap;
    }

    /**
     * 递归收集字段上的 KylinToolParam 注解
     *
     * @param clazz 要扫描的类
     * @param pathPrefix JsonPath 前缀
     * @param annotationMap 收集注解的 Map
     */
    private static void collectFieldAnnotations(Class<?> clazz, String pathPrefix, Map<String, KylinToolParam> annotationMap) {
        if (clazz == null || clazz == Object.class) {
            return;
        }

        // 获取当前类的所有声明字段（不包括继承的字段）
        Field[] fields = clazz.getDeclaredFields();

        for (Field field : fields) {
            // 构建当前字段的 JsonPath
            String fieldPath = pathPrefix.isEmpty() ? field.getName() : pathPrefix + "." + field.getName();

            // 检查字段是否有 KylinToolParam 注解
            KylinToolParam annotation = field.getAnnotation(KylinToolParam.class);
            if (annotation != null) {
                annotationMap.put(fieldPath, annotation);
            }

            // 如果字段类型是自定义类（非基本类型、包装类、String等），递归处理
            Class<?> fieldType = field.getType();
            if (shouldRecurseIntoType(fieldType)) {
                collectFieldAnnotations(fieldType, fieldPath, annotationMap);
            }
        }

        // 处理父类字段
        collectFieldAnnotations(clazz.getSuperclass(), pathPrefix, annotationMap);
    }

    /**
     * 判断是否需要递归处理该类型的字段
     *
     * @param type 字段类型
     * @return 是否需要递归
     */
    private static boolean shouldRecurseIntoType(Class<?> type) {
        // 跳过基本类型
        if (type.isPrimitive()) {
            return false;
        }

        // 跳过包装类型
        if (type == Boolean.class || type == Byte.class || type == Character.class ||
            type == Short.class || type == Integer.class || type == Long.class ||
            type == Float.class || type == Double.class) {
            return false;
        }

        // 跳过常用类型
        if (type == String.class || type.isArray() || type.isEnum()) {
            return false;
        }

        // 跳过 java.* 和 javax.* 包下的类
        String packageName = type.getPackage() != null ? type.getPackage().getName() : "";
        if (packageName.startsWith("java.") || packageName.startsWith("javax.")) {
            return false;
        }

        // 其他情况认为是自定义类，需要递归处理
        return true;
    }

    /**
     * GET 方法，会根据所有参数，先组装成 Map，然后再生成 JsonSchema
     */
    private static String parseGet(Method method) {
        Parameter[] params = method.getParameters();
        if (params == null || params.length == 0) {
            return "{}"; // 如果没有参数，返回空的 JSON 对象
        }

        StringBuilder schemaBuilder = new StringBuilder("{ \"type\": \"object\", \"properties\": ");
        List<String> required = new ArrayList<>();
        processParams(params, required, schemaBuilder);
        processRequired(schemaBuilder, required);
        schemaBuilder.append(", \"additionalProperties\": false");
        schemaBuilder.append("}");
        return schemaBuilder.toString();
    }

    private static void processParams(Parameter[] params, List<String> required, StringBuilder schemaBuilder) {
        schemaBuilder.append("{");
        for (int i = 0; i < params.length; i++) {
            Parameter param = params[i];
            String name = param.getName();
            KylinToolParam paramAnnotation = param.getAnnotation(KylinToolParam.class);
            String s = "\""+name+"\":"+JsonSchemaUtils.generateSchema(param.getType());
            if (paramAnnotation != null) {
                // 自己组装 JsonSchema
                JSONObject entries = new JSONObject();
                entries.set("type", paramAnnotation.type().getType());
                entries.set("description", paramAnnotation.description());
                s = String.format("\"%s\":%s", name, entries);

                if (paramAnnotation.required()) {
                    required.add(name);
                }
            }
            schemaBuilder.append(s);
            if (i < params.length - 1) {
                schemaBuilder.append(", ");
            }
        }
        schemaBuilder.append("}");
    }

    private static void processRequired(StringBuilder schemaBuilder, List<String> required) {
        if (required.isEmpty()) {
            return;
        }

        schemaBuilder.append(", \"required\": [");
        for (int i = 0; i < required.size(); i++) {
            schemaBuilder.append("\"").append(required.get(i)).append("\"");
            if (i < required.size() - 1) {
                schemaBuilder.append(", ");
            }
        }
        schemaBuilder.append("]");
    }
}
