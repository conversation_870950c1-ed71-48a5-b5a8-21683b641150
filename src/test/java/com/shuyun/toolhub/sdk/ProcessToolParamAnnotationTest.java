package com.shuyun.toolhub.sdk;

import com.shuyun.toolhub.sdk.annotations.KylinToolParam;
import com.shuyun.toolhub.sdk.enums.ObjectType;
import com.shuyun.toolhub.sdk.utils.InputSchemaParser;

import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;

/**
 * 测试 processToolParamAnnotation 方法
 */
public class ProcessToolParamAnnotationTest {

    public static void main(String[] args) {
        System.out.println("开始测试 processToolParamAnnotation 方法...");
        
        try {
            testProcessToolParamAnnotation();
            System.out.println("\n🎉 所有测试通过！processToolParamAnnotation 方法实现正确！");
            
        } catch (Exception e) {
            System.err.println("❌ 测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void testProcessToolParamAnnotation() throws Exception {
        // 模拟一个简单的 JSON Schema
        String originalSchema = "{\n" +
            "  \"type\" : \"object\",\n" +
            "  \"properties\" : {\n" +
            "    \"name\" : {\n" +
            "      \"type\" : \"string\"\n" +
            "    },\n" +
            "    \"age\" : {\n" +
            "      \"type\" : \"integer\"\n" +
            "    },\n" +
            "    \"address\" : {\n" +
            "      \"type\" : \"object\",\n" +
            "      \"properties\" : {\n" +
            "        \"city\" : {\n" +
            "          \"type\" : \"string\"\n" +
            "        },\n" +
            "        \"street\" : {\n" +
            "          \"type\" : \"string\"\n" +
            "        }\n" +
            "      }\n" +
            "    }\n" +
            "  },\n" +
            "  \"additionalProperties\" : false\n" +
            "}";
        
        // 创建注解映射
        Map<String, KylinToolParam> annotations = new HashMap<>();
        
        // 模拟注解（由于无法直接创建注解实例，我们使用反射调用方法）
        annotations.put("name", createMockAnnotation(ObjectType.STRING, "用户名", true));
        annotations.put("age", createMockAnnotation(ObjectType.INTEGER, "年龄", false));
        annotations.put("address", createMockAnnotation(ObjectType.OBJECT, "用户地址", false));
        annotations.put("address.city", createMockAnnotation(ObjectType.STRING, "城市", true));
        annotations.put("address.street", createMockAnnotation(ObjectType.STRING, "街道", false));
        
        // 使用反射调用私有方法
        Method processMethod = InputSchemaParser.class.getDeclaredMethod("processToolParamAnnotation", String.class, Map.class);
        processMethod.setAccessible(true);
        
        String result = (String) processMethod.invoke(null, originalSchema, annotations);
        
        System.out.println("原始 Schema:");
        System.out.println(originalSchema);
        System.out.println("\n处理后的 Schema:");
        System.out.println(result);
        
        // 验证结果
        if (result.contains("\"description\" : \"用户名\"")) {
            System.out.println("✅ name 字段的 description 更新成功");
        } else {
            System.err.println("❌ name 字段的 description 更新失败");
        }
        
        if (result.contains("\"description\" : \"年龄\"")) {
            System.out.println("✅ age 字段的 description 更新成功");
        } else {
            System.err.println("❌ age 字段的 description 更新失败");
        }
        
        if (result.contains("\"description\" : \"城市\"")) {
            System.out.println("✅ address.city 字段的 description 更新成功");
        } else {
            System.err.println("❌ address.city 字段的 description 更新失败");
        }
        
        if (result.contains("\"required\"") && result.contains("\"name\"") && result.contains("\"address\"")) {
            System.out.println("✅ required 字段更新成功");
        } else {
            System.err.println("❌ required 字段更新失败");
        }
    }
    
    // 创建模拟的 KylinToolParam 注解
    private static KylinToolParam createMockAnnotation(ObjectType type, String description, boolean required) {
        return new KylinToolParam() {
            @Override
            public ObjectType type() {
                return type;
            }
            
            @Override
            public String description() {
                return description;
            }
            
            @Override
            public boolean required() {
                return required;
            }
            
            @Override
            public Class<? extends java.lang.annotation.Annotation> annotationType() {
                return KylinToolParam.class;
            }
        };
    }
}
