package com.shuyun.toolhub.sdk;

import com.shuyun.toolhub.sdk.annotations.KylinToolParam;
import com.shuyun.toolhub.sdk.enums.ObjectType;
import com.shuyun.toolhub.sdk.utils.InputSchemaParser;
import lombok.Data;

import java.lang.reflect.Method;
import java.util.Map;

/**
 * 测试 collectToolParamAnnotation 方法
 */
public class CollectToolParamAnnotationTest {

    // 测试用的嵌套类
    @Data
    static class TestUser {
        @KylinToolParam(type = ObjectType.STRING, description = "用户名", required = true)
        private String name;
        
        @KylinToolParam(type = ObjectType.INTEGER, description = "年龄")
        private Integer age;
        
        private String email; // 没有注解的字段
        
        @KylinToolParam(type = ObjectType.OBJECT, description = "用户地址")
        private Address address;
    }
    
    @Data
    static class Address {
        @KylinToolParam(type = ObjectType.STRING, description = "城市", required = true)
        private String city;
        
        @KylinToolParam(type = ObjectType.STRING, description = "街道")
        private String street;
        
        private String zipCode; // 没有注解的字段
    }

    public static void main(String[] args) {
        try {
            testCollectToolParamAnnotation();
            System.out.println("所有测试通过！");
        } catch (Exception e) {
            System.err.println("测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private static void testCollectToolParamAnnotation() throws Exception {
        System.out.println("测试 collectToolParamAnnotation 方法...");
        
        // 使用反射调用私有方法
        Method collectMethod = InputSchemaParser.class.getDeclaredMethod("collectToolParamAnnotation", Class.class);
        collectMethod.setAccessible(true);
        
        @SuppressWarnings("unchecked")
        Map<String, KylinToolParam> result = (Map<String, KylinToolParam>) collectMethod.invoke(null, TestUser.class);
        
        System.out.println("收集到的注解映射:");
        for (Map.Entry<String, KylinToolParam> entry : result.entrySet()) {
            KylinToolParam annotation = entry.getValue();
            System.out.printf("  %s -> type: %s, description: %s, required: %s%n", 
                entry.getKey(), 
                annotation.type().getType(), 
                annotation.description(), 
                annotation.required());
        }
        
        // 验证结果
        if (result.size() != 5) {
            throw new AssertionError("期望收集到5个注解，但实际收集到: " + result.size());
        }
        
        // 验证顶层字段
        assertAnnotationExists(result, "name", ObjectType.STRING, "用户名", true);
        assertAnnotationExists(result, "age", ObjectType.INTEGER, "年龄", false);
        assertAnnotationExists(result, "address", ObjectType.OBJECT, "用户地址", false);
        
        // 验证嵌套字段
        assertAnnotationExists(result, "address.city", ObjectType.STRING, "城市", true);
        assertAnnotationExists(result, "address.street", ObjectType.STRING, "街道", false);
        
        // 验证没有注解的字段不会被收集
        if (result.containsKey("email") || result.containsKey("address.zipCode")) {
            throw new AssertionError("不应该收集没有注解的字段");
        }
        
        System.out.println("collectToolParamAnnotation 方法测试通过");
    }
    
    private static void assertAnnotationExists(Map<String, KylinToolParam> result, String path, 
                                             ObjectType expectedType, String expectedDescription, 
                                             boolean expectedRequired) {
        if (!result.containsKey(path)) {
            throw new AssertionError("缺少路径: " + path);
        }
        
        KylinToolParam annotation = result.get(path);
        if (annotation.type() != expectedType) {
            throw new AssertionError("路径 " + path + " 的类型不匹配，期望: " + expectedType + "，实际: " + annotation.type());
        }
        
        if (!annotation.description().equals(expectedDescription)) {
            throw new AssertionError("路径 " + path + " 的描述不匹配，期望: " + expectedDescription + "，实际: " + annotation.description());
        }
        
        if (annotation.required() != expectedRequired) {
            throw new AssertionError("路径 " + path + " 的required不匹配，期望: " + expectedRequired + "，实际: " + annotation.required());
        }
    }
}
