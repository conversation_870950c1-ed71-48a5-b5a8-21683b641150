package com.shuyun.toolhub.sdk;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyDescription;
import com.shuyun.toolhub.sdk.annotations.KylinToolParam;
import com.shuyun.toolhub.sdk.enums.HttpMethod;
import com.shuyun.toolhub.sdk.enums.ObjectType;
import com.shuyun.toolhub.sdk.utils.InputSchemaParser;
import lombok.Data;

import java.lang.reflect.Method;

public class InputSchemaParserTest {

    // 测试用的内部类
    static class TestController {
        public void noParamMethod() {
            // 无参数方法
        }

        public void getMethodWithParams(
                @KylinToolParam(description = "用户ID", required = true, type = ObjectType.INTEGER) Long userId,
                @KylinToolParam(description = "用户名", type = ObjectType.STRING) String username,
                Integer age
                ) {
            // 带参数的GET方法
        }

        public void postMethodWithBody(TestRequestBody body) {
            // POST方法带请求体
        }
    }

    // 测试请求体类
    @Data
    static class TestRequestBody {
        @JsonProperty(required = true)
        @JsonPropertyDescription("用户姓名")
        private String name;

        @JsonPropertyDescription("年龄")
        private int age;

        @KylinToolParam(type = ObjectType.INTEGER, description = "性别", required = true)
        private int sex;
    }
    
    public static void main(String[] args) {
        try {
            // 测试1: 无参数GET方法
            testParseGetWithNoParams();
            
            // 测试2: 带参数GET方法
            testParseGetWithParams();
            
            // 测试3: 带请求体POST方法
            testParsePostWithBody();
            
            // 测试4: 无参数POST方法
            testParsePostWithNoParams();
            
            System.out.println("所有测试通过!");
        } catch (Exception e) {
            System.err.println("测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void testParseGetWithNoParams() throws NoSuchMethodException {
        System.out.println("测试无参数GET方法...");
        Method method = TestController.class.getMethod("noParamMethod");
        String schema = InputSchemaParser.parse(HttpMethod.GET, method);
        
        if (!schema.equals("{}")) {
            throw new AssertionError("无参数的GET方法应返回空的JSON对象，但实际返回: " + schema);
        }
        System.out.println("无参数GET方法测试通过");
    }
    
    private static void testParseGetWithParams() throws NoSuchMethodException {
        System.out.println("\n测试带参数GET方法...");
        Method method = TestController.class.getMethod("getMethodWithParams", Long.class, String.class, Integer.class);
        String schema = InputSchemaParser.parse(HttpMethod.GET, method);
        
        System.out.println("生成的schema: " + schema);
        
        // 验证schema包含必要的元素
        assertContains(schema, "\"type\": \"object\"", "应包含object类型");
        assertContains(schema, "\"properties\"", "应包含properties字段");
        assertContains(schema, "\"userId\"", "应包含userId参数");
        assertContains(schema, "\"username\"", "应包含username参数");
        assertContains(schema, "\"integer\"", "应包含integer类型");
        assertContains(schema, "\"string\"", "应包含string类型");
        assertContains(schema, "\"required\"", "应包含required字段");
        assertContains(schema, "\"userId\"", "required字段中应包含userId");
        
        // 验证username不在required字段中
        if (schema.contains("\"required\": [") && schema.contains("\"username\"")) {
            int requiredIndex = schema.indexOf("\"required\": [");
            int requiredEndIndex = schema.indexOf("]", requiredIndex);
            String requiredSection = schema.substring(requiredIndex, requiredEndIndex);
            
            if (requiredSection.contains("\"username\"")) {
                throw new AssertionError("required字段中不应包含username");
            }
        }
        
        System.out.println("带参数GET方法测试通过");
    }
    
    private static void testParsePostWithBody() throws NoSuchMethodException {
        System.out.println("\n测试带请求体POST方法...");
        Method method = TestController.class.getMethod("postMethodWithBody", TestRequestBody.class);
        String schema = InputSchemaParser.parse(HttpMethod.POST, method);
        
        System.out.println("生成的schema: " + schema);
        
        if (schema.equals("{}")) {
            throw new AssertionError("有参数的POST方法不应返回空对象");
        }
        
        System.out.println("带请求体POST方法测试通过");
    }
    
    private static void testParsePostWithNoParams() throws NoSuchMethodException {
        System.out.println("\n测试无参数POST方法...");
        Method method = TestController.class.getMethod("noParamMethod");
        String schema = InputSchemaParser.parse(HttpMethod.POST, method);
        
        if (!schema.equals("{}")) {
            throw new AssertionError("无参数的POST方法应返回空的JSON对象，但实际返回: " + schema);
        }
        
        System.out.println("无参数POST方法测试通过");
    }
    
    private static void assertContains(String actual, String expected, String message) {
        if (!actual.contains(expected)) {
            throw new AssertionError(message + "，期望包含: " + expected + "，但实际值: " + actual);
        }
    }
}